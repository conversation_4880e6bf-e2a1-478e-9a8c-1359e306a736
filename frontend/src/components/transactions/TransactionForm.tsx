import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
  ArrowLeftRight,
  CalendarIcon,
  FileText,
  RefreshCcw,
  Save
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { useBankAccounts, usePaymentMethodOptions, useRecurrenceTypeOptions } from "@/hooks/api";
import { useActiveCompany } from "@/hooks/useActiveCompany";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Transaction } from "@/types/transaction";
import TransactionAmountField from "@/components/transactions/TransactionAmountField";

const transactionSchema = z.object({
  type: z.enum(["accounts_receivable", "accounts_payable", "transfer"]),
  description: z.string().min(1, "A descrição é obrigatória"),
  amount: z.number().min(0.01, "O valor deve ser maior que zero"),
  transactionDate: z.date(),
  bankAccountId: z.string().min(1, "A conta de origem é obrigatória"),
  destinationAccountId: z.string().optional(),
  relatedId: z.string().optional(),
  notes: z.string().optional(),
  recurrence: z.string().optional(),
  paymentMethod: z.string().optional(),
  invoiceNumber: z.string().optional(),
});

export type TransactionFormValues = z.infer<typeof transactionSchema>;

interface TransactionFormProps {
  isCreating: boolean;
  isEditing: boolean;
  isViewing: boolean;
  initialTransaction?: Transaction | null;
  initialType: "accounts_receivable" | "accounts_payable" | "transfer";
  onSubmit: (data: TransactionFormValues) => void;
}



export const TransactionForm = ({
  isCreating,
  isEditing,
  isViewing,
  initialTransaction,
  initialType,
  onSubmit,
}: TransactionFormProps) => {
  const [refreshingFields, setRefreshingFields] = useState<Record<string, boolean>>({
    bankAccountId: false,
    destinationAccountId: false,
    paymentMethod: false,
    recurrence: false
  });
  
  const navigate = useNavigate();
  const activeCompanyId = useActiveCompany();
  
  // Buscar contas bancárias reais do banco de dados
  const { data: bankAccountsData, isLoading: loadingBankAccounts, refetch: refetchBankAccounts } = useBankAccounts(
    1,
    100, // Buscar até 100 contas para o dropdown
    {
      companyId: activeCompanyId
    }
  );
  
  const bankAccounts = bankAccountsData?.data || [];

  // Buscar métodos de pagamento e tipos de recorrência da API
  const { data: paymentMethods, isLoading: loadingPaymentMethods } = usePaymentMethodOptions();
  const { data: recurrenceOptions, isLoading: loadingRecurrenceTypes } = useRecurrenceTypeOptions();

  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      type: initialType,
      description: initialTransaction?.description || "",
      amount: initialTransaction?.amount ? Math.abs(initialTransaction.amount) : 0,
      transactionDate: initialTransaction?.transactionDate || new Date(),
      bankAccountId: initialTransaction?.bankAccountId || "",
      destinationAccountId: (initialTransaction as any)?.destinationAccountId || "",
      relatedId: initialTransaction?.relatedId || "",
      notes: (initialTransaction as any)?.notes || "",
      recurrence: (initialTransaction as any)?.recurrence || "none",
      paymentMethod: (initialTransaction as any)?.paymentMethod || "",
      invoiceNumber: (initialTransaction as any)?.invoiceNumber || "",
    }
  });

  const transactionType = form.watch("type");

  const refreshFieldOptions = (field: string) => {
    setRefreshingFields(prev => ({
      ...prev,
      [field]: true
    }));
    
    // Se for bankAccountId ou destinationAccountId, fazer refetch das contas bancárias
    if (field === 'bankAccountId' || field === 'destinationAccountId') {
      refetchBankAccounts();
    }
    
    setTimeout(() => {
      setRefreshingFields(prev => ({
        ...prev,
        [field]: false
      }));
      
      const fieldDisplayNames: Record<string, string> = {
        bankAccountId: "contas bancárias",
        destinationAccountId: "contas de destino",
        paymentMethod: "métodos de pagamento",
        recurrence: "tipos de recorrência"
      };
      
      toast({
        title: "Opções atualizadas",
        description: `A lista de ${fieldDisplayNames[field]} foi atualizada com sucesso.`,
      });
    }, 800);
  };

  const handleSubmit = (data: TransactionFormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tipo de Transação</FormLabel>
              <Select 
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={!isCreating || isViewing}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tipo" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="accounts_receivable">Receita</SelectItem>
                  <SelectItem value="accounts_payable">Despesa</SelectItem>
                  <SelectItem value="transfer">
                    <div className="flex items-center">
                      <ArrowLeftRight className="mr-2 h-4 w-4" />
                      <span>Transferência</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Descrição</FormLabel>
              <FormControl>
                <Input
                  placeholder="Descrição da transação"
                  disabled={isViewing}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="amount"
            render={({ field: { onChange, value, ...rest } }) => (
              <FormItem>
                <FormLabel>Valor</FormLabel>
                <FormControl>
                  <TransactionAmountField
                    value={value}
                    onChange={onChange}
                    isMainAmount={true}
                    disabled={isViewing}
                    label="Valor da Transação"
                    id="transaction-amount"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="transactionDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Data da Transação</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                        disabled={isViewing}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, "dd/MM/yyyy")
                        ) : (
                          <span>Selecione uma data</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="bankAccountId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{transactionType === "transfer" ? "Conta de Origem" : "Conta Bancária"}</FormLabel>
                <div className="flex space-x-2">
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isViewing}
                  >
                    <FormControl>
                      <SelectTrigger className={cn("w-full", refreshingFields.bankAccountId && "animate-pulse")}>
                        <SelectValue placeholder="Selecione uma conta" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {bankAccounts.map((account) => (
                        <SelectItem key={account.id} value={account.id}>
                          {account.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    size="icon"
                    variant="outline"
                    onClick={() => refreshFieldOptions("bankAccountId")}
                    disabled={refreshingFields.bankAccountId || isViewing}
                  >
                    <RefreshCcw className={cn("h-4 w-4", refreshingFields.bankAccountId && "animate-spin")} />
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {transactionType === "transfer" && (
            <FormField
              control={form.control}
              name="destinationAccountId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Conta de Destino</FormLabel>
                  <div className="flex space-x-2">
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isViewing}
                    >
                      <FormControl>
                        <SelectTrigger className={cn("w-full", refreshingFields.destinationAccountId && "animate-pulse")}>
                          <SelectValue placeholder="Selecione a conta de destino" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {bankAccounts
                          .filter((account) => account.id !== form.watch("bankAccountId"))
                          .map((account) => (
                            <SelectItem key={account.id} value={account.id}>
                              {account.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      size="icon"
                      variant="outline"
                      onClick={() => refreshFieldOptions("destinationAccountId")}
                      disabled={refreshingFields.destinationAccountId || isViewing}
                    >
                      <RefreshCcw className={cn("h-4 w-4", refreshingFields.destinationAccountId && "animate-spin")} />
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {transactionType !== "transfer" && (
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Método de Pagamento</FormLabel>
                  <div className="flex space-x-2">
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isViewing}
                    >
                      <FormControl>
                        <SelectTrigger className={cn("w-full", refreshingFields.paymentMethod && "animate-pulse")}>
                          <SelectValue placeholder="Selecione um método" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {paymentMethods.map((method) => (
                          <SelectItem key={method.id} value={method.id}>
                            {method.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      type="button"
                      size="icon"
                      variant="outline"
                      onClick={() => refreshFieldOptions("paymentMethod")}
                      disabled={refreshingFields.paymentMethod || isViewing}
                    >
                      <RefreshCcw className={cn("h-4 w-4", refreshingFields.paymentMethod && "animate-spin")} />
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="recurrence"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Recorrência</FormLabel>
                <div className="flex space-x-2">
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isViewing}
                  >
                    <FormControl>
                      <SelectTrigger className={cn("w-full", refreshingFields.recurrence && "animate-pulse")}>
                        <SelectValue placeholder="Selecione a recorrência" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {recurrenceOptions.map((option) => (
                        <SelectItem key={option.id} value={option.id}>
                          {option.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    type="button"
                    size="icon"
                    variant="outline"
                    onClick={() => refreshFieldOptions("recurrence")}
                    disabled={refreshingFields.recurrence || isViewing}
                  >
                    <RefreshCcw className={cn("h-4 w-4", refreshingFields.recurrence && "animate-spin")} />
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {transactionType !== "transfer" && (
            <FormField
              control={form.control}
              name="invoiceNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{transactionType === "accounts_receivable" ? "Número da Fatura/NF" : "Número do Boleto/NF"}</FormLabel>
                  <FormControl>
                    <div className="flex">
                      <div className="relative flex-1">
                        <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="pl-9"
                          placeholder={transactionType === "accounts_receivable" ? "Fatura/NF" : "Boleto/NF"}
                          disabled={isViewing}
                          {...field}
                        />
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Observações</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Observações adicionais sobre a transação"
                  className="resize-none min-h-[100px]"
                  disabled={isViewing}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {!isViewing && (
          <div className="flex justify-end">
            <Button type="submit" disabled={form.formState.isSubmitting} className="w-full md:w-auto">
              <Save className="mr-2 h-4 w-4" />
              {isEditing ? "Atualizar Transação" : "Criar Transação"}
            </Button>
          </div>
        )}
      </form>
    </Form>
  );
};
