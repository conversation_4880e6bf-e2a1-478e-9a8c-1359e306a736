// Tipos comuns
export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

// Auth
export type LoginRequest = {
  email: string;
  password: string;
};

export type RegisterRequest = {
  email: string;
  password: string;
  passwordConfirmation: string;
  profile: {
    username: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
};

export type LoginResponse = {
  accessToken: string;
  refreshToken: string;
  user: User;
};

export type RefreshTokenRequest = {
  refreshToken: string;
};

// User
export type User = {
  id: string;
  email: string;
  status: string;
  profile?: UserProfile;
  role?: Role;
  active?: boolean;
  createdAt: string;
  updatedAt: string;
};

export type UserProfile = {
  username: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatarUrl?: string;
  preferences?: any;
  isActive: boolean;
};

export type CreateUserRequest = {
  email: string;
  password: string;
  status?: string;
  profile: {
    username: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    avatarUrl?: string;
    preferences?: any;
  };
  roleId?: string;
};

export type UpdateUserRequest = {
  email?: string;
  password?: string;
  status?: string;
  profile?: {
    username?: string;
    firstName?: string;
    lastName?: string;
    phone?: string;
    avatarUrl?: string;
    preferences?: any;
    isActive?: boolean;
  };
  roleId?: string;
};

// Company
export type Company = {
  id: string;
  name: string;
  cnpj?: string;
  phone?: string;
  email?: string;
  address?: Address;
  addressId?: string;
  logo?: string;
  active?: boolean;
  calendarType?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type CreateCompanyRequest = {
  name: string;
  cnpj: string;
  phone?: string;
  email?: string;
  addressId?: string;
  logo?: string;
  active?: boolean;
  calendarType?: string;
};

export type UpdateCompanyRequest = {
  name?: string;
  cnpj?: string;
  phone?: string;
  email?: string;
  addressId?: string;
  logo?: string;
  active?: boolean;
  calendarType?: string;
};

export type CompanySettings = {
  id: string;
  companyId: string;
  fiscalYearStart: number;
  fiscalYearEnd: number;
  defaultCurrency: string;
  dateFormat: string;
  timezone: string;
  createdAt: string;
  updatedAt: string;
};

// Account Payable
export type AccountPayable = {
  id: string;
  description: string;
  amount: number;
  dueDate: string;
  issueDate: string;
  paymentDate?: string;
  status: 'pending' | 'paid' | 'cancelled';
  categoryId: string;
  category?: Category;
  entityId: string;
  entity?: Entity;
  bankAccountId?: string;
  bankAccount?: BankAccount;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateAccountPayableRequest = {
  description: string;
  amount: number;
  dueDate: string;
  issueDate: string;
  categoryId: string;
  entityId: string;
  bankAccountId?: string;
  companyId: string;
};

export type UpdateAccountPayableRequest = {
  description?: string;
  amount?: number;
  dueDate?: string;
  issueDate?: string;
  categoryId?: string;
  entityId?: string;
  bankAccountId?: string;
  status?: 'pending' | 'paid' | 'cancelled';
};

// Account Receivable
export type AccountReceivable = {
  id: string;
  description: string;
  amount: number;
  dueDate: string;
  issueDate: string;
  receiveDate?: string;
  status: 'pending' | 'received' | 'cancelled';
  categoryId: string;
  category?: Category;
  entityId: string;
  entity?: Entity;
  bankAccountId?: string;
  bankAccount?: BankAccount;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateAccountReceivableRequest = {
  description: string;
  amount: number;
  dueDate: string;
  issueDate: string;
  categoryId: string;
  entityId: string;
  bankAccountId?: string;
  companyId: string;
};

export type UpdateAccountReceivableRequest = {
  description?: string;
  amount?: number;
  dueDate?: string;
  issueDate?: string;
  categoryId?: string;
  entityId?: string;
  bankAccountId?: string;
  status?: 'pending' | 'received' | 'cancelled';
};

// Transaction
export type Transaction = {
  id: string;
  description: string;
  amount: number;
  date: string;
  type: 'income' | 'expense' | 'transfer';
  status: 'completed' | 'pending' | 'cancelled';
  categoryId: string;
  category?: Category;
  entityId?: string;
  entity?: Entity;
  bankAccountId: string;
  bankAccount?: BankAccount;
  destinationBankAccountId?: string;
  destinationBankAccount?: BankAccount;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateTransactionRequest = {
  companyId?: string; // Opcional conforme DTO do backend
  type: 'income' | 'expense' | 'transfer';
  description: string;
  amount: number;
  transactionDate: string; // Corrigido para alinhar com o backend
  bankAccountId: string;
  destinationAccountId?: string; // Corrigido nome do campo
  accountsPayableId?: string; // Adicionado conforme DTO do backend
  accountsReceivableId?: string; // Adicionado conforme DTO do backend
  categoryId?: string; // Opcional conforme DTO do backend
  entityId?: string;
  projectId?: string; // Adicionado conforme DTO do backend
  paymentMethodId?: string; // Adicionado conforme DTO do backend
  notes?: string; // Adicionado conforme DTO do backend
};

export type UpdateTransactionRequest = {
  description?: string;
  amount?: number;
  date?: string;
  type?: 'income' | 'expense' | 'transfer';
  status?: 'completed' | 'pending' | 'cancelled';
  categoryId?: string;
  entityId?: string;
  bankAccountId?: string;
  destinationBankAccountId?: string;
};

// Bank Account
export type BankAccount = {
  id: string;
  name: string;
  accountNumber?: string;
  agency?: string;
  bankId?: string;
  bank?: Bank;
  initialBalance: number;
  currentBalance: number;
  type: 'checking' | 'savings' | 'investment' | 'cash' | 'other';
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateBankAccountRequest = {
  name: string;
  accountNumber?: string;
  // Removido campo agency que não é esperado pelo backend
  bankId?: string;
  initialBalance: number;
  type: 'checking' | 'savings' | 'investment' | 'cash' | 'other';
  companyId: string;
};

export type UpdateBankAccountRequest = {
  name?: string;
  accountNumber?: string;
  // Removido campo agency que não é esperado pelo backend
  bankId?: string;
  initialBalance?: number;
  type?: 'checking' | 'savings' | 'investment' | 'cash' | 'other';
};

export type BankAccountBalance = {
  currentBalance: number;
  availableBalance: number;
  pendingIncome: number;
  pendingExpense: number;
};

// Bank
export type Bank = {
  id: string;
  name: string;
  code: string;
  logoUrl?: string;
  createdAt: string;
  updatedAt: string;
};

export type CreateBankRequest = {
  name: string;
  code: string;
  logoUrl?: string;
};

export type UpdateBankRequest = {
  name?: string;
  code?: string;
  logoUrl?: string;
};

// Category
export type Category = {
  id: string;
  name: string;
  transactionType: 'payable' | 'receivable';
  parentCategoryId?: string | null;
  parent?: Category;
  children?: Category[];
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
};

export type CreateCategoryRequest = {
  name: string;
  transactionType: 'payable' | 'receivable';
  parentCategoryId?: string | null;
  companyId?: string;
};

export type UpdateCategoryRequest = {
  name?: string;
  transactionType?: 'payable' | 'receivable';
  parentCategoryId?: string | null;
};

// Entity
export type Entity = {
  id: string;
  name: string;
  type: 'client' | 'supplier' | 'both';
  document?: string;
  documentType?: 'cpf' | 'cnpj' | 'other';
  email?: string;
  phone?: string;
  addressId?: string;
  address?: Address;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateEntityRequest = {
  name: string;
  type: 'client' | 'supplier' | 'both';
  document?: string;
  documentType?: 'cpf' | 'cnpj' | 'other';
  email?: string;
  phone?: string;
  addressId?: string;
  companyId: string;
};

export type UpdateEntityRequest = {
  name?: string;
  type?: 'client' | 'supplier' | 'both';
  document?: string;
  documentType?: 'cpf' | 'cnpj' | 'other';
  email?: string;
  phone?: string;
  addressId?: string;
};

// Address
export type Address = {
  id: string;
  street: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  createdAt: string;
  updatedAt: string;
};

export type CreateAddressRequest = {
  street: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
};

export type UpdateAddressRequest = {
  street?: string;
  number?: string;
  complement?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
};

// ZipCode
export type ZipCodeResponse = {
  zipCode: string;
  street: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  ibgeCode?: string;
  country: string;
};

export type CacheZipCodeRequest = {
  zipCode: string;
  street: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  ibgeCode?: string;
  country: string;
};

// Project
export type Project = {
  id: string;
  name: string;
  description?: string;
  status: 'planning' | 'in_progress' | 'completed' | 'cancelled';
  startDate?: string;
  endDate?: string;
  budget?: number;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateProjectRequest = {
  name: string;
  description?: string;
  status: 'planning' | 'in_progress' | 'completed' | 'cancelled';
  startDate?: string;
  endDate?: string;
  budget?: number;
  companyId: string;
};

export type UpdateProjectRequest = {
  name?: string;
  description?: string;
  status?: 'planning' | 'in_progress' | 'completed' | 'cancelled';
  startDate?: string;
  endDate?: string;
  budget?: number;
};

// Role
export type Role = {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
};

export type CreateRoleRequest = {
  name: string;
  description?: string;
  permissionIds: string[];
};

export type UpdateRoleRequest = {
  name?: string;
  description?: string;
  permissionIds?: string[];
};

// Permission
export type Permission = {
  id: string;
  name: string;
  description?: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage';
  createdAt: string;
  updatedAt: string;
};

export type CreatePermissionRequest = {
  name: string;
  description?: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage';
};

export type UpdatePermissionRequest = {
  name?: string;
  description?: string;
  resource?: string;
  action?: 'create' | 'read' | 'update' | 'delete' | 'manage';
};

// Custom Period
export type CustomPeriod = {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  companyId: string;
  company?: Company;
  createdAt: string;
  updatedAt: string;
};

export type CreateCustomPeriodRequest = {
  name: string;
  startDate: string;
  endDate: string;
  companyId: string;
};

export type UpdateCustomPeriodRequest = {
  name?: string;
  startDate?: string;
  endDate?: string;
};

// Reports
export type CashFlowReport = {
  period: {
    start: string;
    end: string;
  };
  summary: {
    initialBalance: number;
    totalIncome: number;
    totalExpense: number;
    finalBalance: number;
    netChange: number;
  };
  details: {
    date: string;
    incomes: {
      categoryId: string;
      categoryName: string;
      amount: number;
    }[];
    expenses: {
      categoryId: string;
      categoryName: string;
      amount: number;
    }[];
    balance: number;
  }[];
};

export type BalanceSheetReport = {
  date: string;
  assets: {
    categoryId: string;
    categoryName: string;
    amount: number;
    items?: {
      id: string;
      name: string;
      amount: number;
    }[];
  }[];
  liabilities: {
    categoryId: string;
    categoryName: string;
    amount: number;
    items?: {
      id: string;
      name: string;
      amount: number;
    }[];
  }[];
  equity: {
    totalAssets: number;
    totalLiabilities: number;
    netWorth: number;
  };
};

export type IncomeStatementReport = {
  period: {
    start: string;
    end: string;
  };
  revenue: {
    categoryId: string;
    categoryName: string;
    amount: number;
    items?: {
      id: string;
      name: string;
      amount: number;
    }[];
  }[];
  expenses: {
    categoryId: string;
    categoryName: string;
    amount: number;
    items?: {
      id: string;
      name: string;
      amount: number;
    }[];
  }[];
  summary: {
    totalRevenue: number;
    totalExpenses: number;
    netIncome: number;
    profitMargin: number;
  };
};

// Notification
export type Notification = {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  userId: string;
  user?: User;
  createdAt: string;
  updatedAt: string;
};

export type CreateNotificationRequest = {
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  userId: string;
};

export type UpdateNotificationRequest = {
  title?: string;
  message?: string;
  type?: 'info' | 'warning' | 'error' | 'success';
  read?: boolean;
};

// Currency
export type Currency = {
  id: string;
  name: string; // Ex: Real Brasileiro, Dólar Americano
  code: string; // Ex: BRL, USD
  symbol: string; // Ex: R$, $
  decimalPlaces: number; // Ex: 2
  isDefault: boolean; // Indica se é a moeda padrão da empresa
  enabled: boolean; // Indica se a moeda está ativa
  createdAt: string;
  updatedAt: string;
};

export type CreateCurrencyRequest = {
  name: string;
  code: string;
  symbol: string;
  decimalPlaces: number;
  isDefault?: boolean;
  enabled?: boolean;
};

export type UpdateCurrencyRequest = {
  name?: string;
  code?: string;
  symbol?: string;
  decimalPlaces?: number;
  isDefault?: boolean;
  enabled?: boolean;
};

// --- Accounts Payable Summary --- New Interface
export interface AccountsPayableSummary {
  totalDue: number;
  totalOverdue: number;
  totalUpcoming: number;
}
